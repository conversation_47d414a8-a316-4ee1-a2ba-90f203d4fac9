import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {P404Component, P500Component} from './home/<USER>/error';
import {LoginComponent} from './home/<USER>/login/login.component';
import {AuthGuard} from './guard/auth.guard';
import {HeaderComponent} from './home/<USER>/layout/header/header.component';
import {PermissionsSidebarComponent} from './home/<USER>/layout/permissions-sidebar/permissions-sidebar.component';
import {FooterComponent} from './home/<USER>/layout/footer/footer.component';
import {P401Component} from './home/<USER>/error/401.component';
import {StarterComponent} from './home/<USER>/starter/starter.component';


// Need to Move these to Core Module
import {UserPermissionsComponent} from './home/<USER>/component/user-permissions/user-permissions.component';
import {DesktopManagerComponent} from './home/<USER>/component/desktop-manager/desktop-manager.component';
import {HomeComponent} from "./home/<USER>";
import {CreateSiComponent} from "./home/<USER>/trade/component/create-si/create-si.component";
import {InvoiceRouterComponent} from "./home/<USER>/trade/component/invoice-router/invoice-router.component";
import {SalesRepInvoiceComponent} from "./home/<USER>/trade/component/sales-rep-invoice/sales-rep-invoice.component";

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'home/starter'
  },
  {
    path: '404',
    component: P404Component
  },
  {
    path: '500',
    component: P500Component
  },
  {
    path: '401',
    component: P401Component
  },
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'new_sales_invoice',
    component: CreateSiComponent
  },
  {
    path: 'sales_rep_invoice',
    component: SalesRepInvoiceComponent
  },
  {
    path: 'invoice_router',
    component: InvoiceRouterComponent
  },
  {
    path: 'home',
    component: HomeComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'starter',
        component: StarterComponent,
      },

      {
        path: 'manage_desktop',
        component: DesktopManagerComponent,
      },
      {
        path: 'user_perms',
        component: UserPermissionsComponent,
      },
      {
        path: 'admin',
        loadChildren: () => import('./home/<USER>/admin.module').then(a => a.AdminModule)
      },
      {
        path: 'inventory',
        loadChildren: () => import('./home/<USER>/inventory/inventory.module').then(m => m.InventoryModule)
      },
      {
        path: 'hr',
        loadChildren: () => import('./home/<USER>/hr/hr.module').then(h => h.HrModule)
      },
      {
        path: 'trade',
        loadChildren: () => import('./home/<USER>/trade/trade.module').then(b => b.TradeModule)
      },
      {
        path: 'report',
        loadChildren: () => import('./home/<USER>/report/report.module').then(b => b.ReportModule)
      },
      {
        path: 'dashboard',
        loadChildren: () => import('./home/<USER>/dashboard/dashboard.module').then(d => d.DashboardModule)
      }
    ]
  },
  // Wildcard route for handling 404 - must be the last route
  {
    path: '**',
    redirectTo: '404'
  }];

export const routeParams = [HeaderComponent, PermissionsSidebarComponent, FooterComponent, P401Component, P404Component, P500Component,
  HomeComponent, UserPermissionsComponent, DesktopManagerComponent, LoginComponent, StarterComponent];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})

export class AppRoutingModule {
}

