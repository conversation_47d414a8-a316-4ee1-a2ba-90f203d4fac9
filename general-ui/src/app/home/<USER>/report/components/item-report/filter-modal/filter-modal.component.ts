import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-item-report-filter-modal',
  templateUrl: './filter-modal.component.html',
  styleUrls: ['./filter-modal.component.css']
})
export class ItemReportFilterModalComponent implements OnInit {

  filterForm: FormGroup;

  // Sort options
  sortOptions = [
    { value: 'itemName', label: 'Item Name' },
    { value: 'itemCode', label: 'Item Code' },
    { value: 'barcode', label: 'Barcode' },
    { value: 'sellingPrice', label: 'Selling Price' },
    { value: 'itemCost', label: 'Item Cost' },
    { value: 'createdDate', label: 'Created Date' }
  ];

  sortDirections = [
    { value: 'asc', label: 'Ascending' },
    { value: 'desc', label: 'Descending' }
  ];

  // Selected filter values to return to parent component
  sortBy: string;
  sortDirection: string;

  // Item property filters
  isWholesale: boolean = null;
  isRetail: boolean = null;
  isManageStock: boolean = null;
  isActive: boolean = null;

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      wholesale: [null],
      retail: [null],
      manageStock: [null],
      active: [null],
      sortBy: ['itemName'],
      sortDirection: ['asc']
    });
  }

  ngOnInit(): void {
    // Initialize with default sort options
    this.sortBy = 'itemName';
    this.sortDirection = 'asc';
  }



  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    // Get form values
    const formValues = this.filterForm.value;
    
    // Set the filter values
    this.sortBy = formValues.sortBy;
    this.sortDirection = formValues.sortDirection;
    this.isWholesale = formValues.wholesale;
    this.isRetail = formValues.retail;
    this.isManageStock = formValues.manageStock;
    this.isActive = formValues.active;

    this.toastr.success('Filters applied successfully', 'Filters');
    this.modalRef.hide();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.sortBy = 'itemName';
    this.sortDirection = 'asc';
    this.isWholesale = null;
    this.isRetail = null;
    this.isManageStock = null;
    this.isActive = null;

    this.filterForm.reset({
      wholesale: null,
      retail: null,
      manageStock: null,
      active: null,
      sortBy: 'itemName',
      sortDirection: 'asc'
    });

    this.toastr.info('All filters cleared', 'Filters');
  }
}
