  import {Brand} from './brand';
import {ItemType} from './item-type';
import {ItemCategory} from './item-category';
import {UOM} from "./uom";
import {Rack} from "./rack";
import {Model} from "./model";
import {Supplier} from "../../trade/model/supplier";

export class Item {

  public id: string;
  public itemCode: string;
  public barcode: string;
  public costCode: string;
  public itemName: string;
  public supplierCode: string;
  public brand: Brand;
  public model: Model;
  // only use to save initial stock quantity in main store
  public quantity: number;
  public prices: Array<number>;
  public itemType: ItemType;
  public itemCategory: ItemCategory;
  public description: string;
  public deadStockLevel: number;
  public sellingPrice: number;
  public itemCost: number;
  public retailDiscount: number;
  public wholesaleDiscount: number;
  public caseQuantity: number;
  public uom: UOM;
  public rack: Rack;
  public supplier: Supplier;
  public manageStock: boolean;
  public active: boolean;
  public wholesale: boolean;
  public retail: boolean;

  // Serial number management for phone shops
  public manageSerial: boolean;
}
