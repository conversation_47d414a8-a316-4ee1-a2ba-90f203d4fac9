<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <strong>Barcoding</strong>
    <button type="button" class="close" aria-label="Close" (click)="modalRef?.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="card-body">
    <div class="modal-body row">
      <div class="col-md-3 form-group">
        <label>BarCode</label>
        <input class="form-control" [(ngModel)]="barcode" disabled>
        <label>Cost Code</label>
        <input class="form-control" [(ngModel)]="costCode" placeholder="Enter cost code">
        <label>No Of Copies</label>
        <input class="form-control" [(ngModel)]="noOfCopies">
        <label>Number of Rows</label>
        <input type="number"
               class="form-control"
               [(ngModel)]="numberOfRows"
               min="1"
               max="20"
               placeholder="Enter number of rows">
      </div>

      <div class="col-md-9" id="print-section"
           style="font-family: sans-serif">
        <table style="width: 100%">
          <tr style="width:100%; text-align: center; vertical-align: middle">
            <td style="margin: 0 !important; padding: 0 !important; text-align:center; vertical-align:center">
              <!--              <div style="text-align: center; font-size: 0.7em; margin: 0 !important; font-weight: bold">{{company.name}}</div>-->
              <ngx-barcode [bc-value]="barcode" [bc-display-value]="true"
                           [bc-element-type]="elementType" [bc-format]="format" [bc-line-color]="lineColor"
                           [bc-width]="width" [bc-height]="height" [bc-display-value]="displayValue"
                           [bc-font-options]="fontOptions" [bc-font]="font" [bc-text-align]="textAlign"
                           [bc-text-position]="textPosition" [bc-text-margin]="textMargin" [bc-font-size]="fontSize"
                           [bc-background]="background" [bc-margin]="margin" [bc-margin-top]="marginTop"
                           [bc-margin-bottom]="marginBottom" [bc-margin-left]="marginLeft"
                           [bc-margin-right]="marginRight">
              </ngx-barcode>
              <div style="text-align: center; font-size: {{previewFontSize}}pt; margin: 0; font-weight: bold; font-family: 'Courier New', monospace;">{{getDisplayCode()}}</div>
              <div style="text-align: center; font-size: {{Math.max(8, previewFontSize - 2)}}pt; margin: 0;font-weight:bolder">{{itemName}}</div>
              <div style="width:100%; text-align: center; font-size: {{Math.max(8, previewFontSize - 1)}}pt; margin: 0; font-weight: bold; color: #007bff;">
                Rs. {{price | number : '1.2-2'}}</div>
            </td>
            <td style="margin: 0 !important; padding: 0 !important; text-align:center; vertical-align:center">
              <!--              <div style="text-align: center; font-size: 0.7em; margin: 0 !important; font-weight: bold">{{company.name}}</div>-->
              <ngx-barcode [bc-value]="barcode" [bc-display-value]="true"
                           [bc-element-type]="elementType" [bc-format]="format" [bc-line-color]="lineColor"
                           [bc-width]="width" [bc-height]="height" [bc-display-value]="displayValue"
                           [bc-font-options]="fontOptions" [bc-font]="font" [bc-text-align]="textAlign"
                           [bc-text-position]="textPosition" [bc-text-margin]="textMargin"
                           [bc-font-size]="fontSize" [bc-margin]="margin" [bc-margin-top]="marginTop"
                           [bc-margin-bottom]="marginBottom" [bc-margin-left]="marginLeft"
                           [bc-margin-right]="marginRight">
              </ngx-barcode>
              <div style="text-align: center; font-size: {{previewFontSize}}pt; margin: 0; font-weight: bold; font-family: 'Courier New', monospace;">{{getDisplayCode()}}</div>
              <div style="text-align: center; font-size: {{Math.max(8, previewFontSize - 2)}}pt;margin: 0;font-weight:bolder;">{{itemName}}</div>
              <div style="width:100%; text-align: center; font-size: {{Math.max(8, previewFontSize - 1)}}pt;margin: 0; font-weight: bold; color: #007bff;">
                Rs. {{price | number : '1.2-2'}}</div>
            </td>
            <td style="margin: 0 !important; padding: 0 !important; text-align:center; vertical-align:center">
              <!--              <div style="text-align: center; font-size: 0.7em;margin: 0 !important; font-weight: bold">{{company.name}}</div>-->
              <ngx-barcode [bc-value]="barcode" [bc-display-value]="true"
                           [bc-element-type]="elementType" [bc-format]="format" [bc-line-color]="lineColor"
                           [bc-width]="width" [bc-height]="height" [bc-display-value]="displayValue"
                           [bc-font-options]="fontOptions" [bc-font]="font" [bc-text-align]="textAlign"
                           [bc-text-position]="textPosition" [bc-text-margin]="textMargin" [bc-font-size]="fontSize"
                           [bc-background]="background" [bc-margin]="margin" [bc-margin-top]="marginTop"
                           [bc-margin-bottom]="marginBottom" [bc-margin-left]="marginLeft"
                           [bc-margin-right]="marginRight">
              </ngx-barcode>
              <div style="text-align: center; font-size: {{previewFontSize}}pt; margin: 0; font-weight: bold; font-family: 'Courier New', monospace;">{{getDisplayCode()}}</div>
              <div
                style="text-align: center; font-size: {{Math.max(8, previewFontSize - 2)}}pt;margin: 0; padding: 0;font-weight:bolder;">{{itemName}}</div>
              <div style="width:100%; text-align: center; font-size: {{Math.max(8, previewFontSize - 1)}}pt;margin: 0; padding: 0; font-weight: bold; color: #007bff;">
                Rs. {{price | number : '1.2-2'}}</div>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="row mt-3 align-content-end d-block mr-2 text-right">
      <button class="btn btn-success" printSectionId="print-section" ngxPrint>
        Print
      </button>
  </div>
