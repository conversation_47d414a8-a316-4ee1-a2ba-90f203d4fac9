<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <strong>Print Barcode</strong>
    <button type="button" class="close" aria-label="Close" (click)="modalRef?.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="card-body">
    <div class="modal-body row">
      <div class="col-md-3 form-group">
        <label>BarCode</label>
        <input class="form-control" [(ngModel)]="barcode" disabled>
        <label>Number of Rows</label>
        <input type="number"
               class="form-control"
               [(ngModel)]="numberOfRows"
               min="1"
               max="20"
               placeholder="Enter number of rows">
      </div>

      <div class="col-md-9" id="print-section" style="font-family: sans-serif">
        <!-- Dynamic preview grid based on rows and columns settings -->
        <div style="display: grid; grid-template-columns: repeat({{numberOfColumns}}, 1fr); gap: 10px; padding: 10px;">
          <div *ngFor="let item of getPreviewItems(); let i = index"
               style="border: 1px solid #ddd; padding: 10px; text-align: center; background-color: #fff; border-radius: 4px; min-height: 120px; display: flex; flex-direction: column; justify-content: center; align-items: center;">

            <!-- NGX Barcode element - always shows original barcode for scanning -->
            <ngx-barcode [bc-value]="getBarcodeValue()"
                         [bc-display-value]="false"
                         [bc-element-type]="elementType"
                         [bc-format]="format"
                         [bc-line-color]="lineColor"
                         [bc-width]="width"
                         [bc-height]="height"
                         [bc-font-options]="fontOptions"
                         [bc-font]="font"
                         [bc-text-align]="textAlign"
                         [bc-text-position]="textPosition"
                         [bc-text-margin]="textMargin"
                         [bc-font-size]="fontSize"
                         [bc-background]="background"
                         [bc-margin]="margin"
                         [bc-margin-top]="marginTop"
                         [bc-margin-bottom]="marginBottom"
                         [bc-margin-left]="marginLeft"
                         [bc-margin-right]="marginRight">
            </ngx-barcode>

            <!-- Display code (cost code if active, otherwise barcode) -->
            <div
              style="text-align: center; font-size: {{previewFontSize}}pt; margin: 2px 0; font-weight: bold; font-family: 'Courier New', monospace; color: #333;">
              {{ item.displayCode }}
            </div>

            <!-- Item name -->
            <div
              style="text-align: center; font-size: {{Math.max(8, previewFontSize - 2)}}pt; margin: 2px 0; font-weight: bold; color: #666; word-wrap: break-word;">
              {{ item.itemName }}
            </div>

            <!-- Price -->
            <div
              style="text-align: center; font-size: {{Math.max(8, previewFontSize - 1)}}pt; margin: 2px 0; font-weight: bold; color: #007bff;">
              Rs. {{ item.price | number : '1.2-2' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-3 align-content-end d-block mr-2 text-right">
      <button class="btn btn-success" printSectionId="print-section" ngxPrint>
        Print
      </button>
    </div>
  </div>
</div>
