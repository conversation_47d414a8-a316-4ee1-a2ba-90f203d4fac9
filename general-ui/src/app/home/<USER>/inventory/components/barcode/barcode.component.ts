import {Component, OnInit} from '@angular/core';
import {CompanyService} from "../../../../core/service/company.service";
import {Company} from "../../../../core/model/company";
import {BsModalRef} from "ngx-bootstrap/modal";
import {BarcodeSettingsService, BarcodeSettings, CostCodeSettings} from "../../../../core/service/barcode-settings.service";

@Component({
  selector: 'app-barcode',
  templateUrl: './barcode.component.html',
  styleUrls: ['./barcode.component.css']
})
export class BarcodeComponent implements OnInit {

  barcode = '';
  itemName = '';
  itemCode = '';
  price = '';
  itemCost = 0; // Item cost for cost code conversion
  numberOfRows: 3;
  numberOfColumns = 2; // Add columns control
  elementType = 'svg';
  format = 'CODE128';
  lineColor = '#000000';
  width = 0.9;
  height = 25;
  displayValue = false;
  fontOptions = '';
  font = 'monospace';
  textAlign = 'center';
  textPosition = 'bottom';
  textMargin = 0;
  fontSize = 9;
  background = '#ffffff';
  margin = 0;
  marginTop = 0;
  marginBottom = 0;
  marginLeft = 0;
  marginRight = 0;

  // Settings
  barcodeSettings: BarcodeSettings;
  costCodeSettings: CostCodeSettings;
  previewFontSize = 12;
  useCostCodes = false;

  // Make Math available in template
  Math = Math;

  company: Company;
  modalRef: BsModalRef;

  constructor(
    private companyService: CompanyService,
    private barcodeSettingsService: BarcodeSettingsService
  ) {
  }

  ngOnInit() {
    this.findCompany();
    this.company = new Company();
    this.loadSettings();
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
    });
  }

  /**
   * Load barcode and cost code settings
   */
  loadSettings() {
    // Load barcode settings from general settings
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettingsFromGeneralSettings();
    this.previewFontSize = this.barcodeSettings.fontSize;
    this.useCostCodes = this.barcodeSettings.useCostCodes;
    this.numberOfColumns = this.barcodeSettings.columns;

    // Load cost code settings
    this.barcodeSettingsService.getCostCodeSettings().subscribe(settings => {
      this.costCodeSettings = settings;
    });
  }

  /**
   * Get the display code for preview (with cost code conversion if enabled)
   */
  getDisplayCode(): string {
    if (this.useCostCodes) {
      // If cost codes are active, use item cost to generate cost code
      if (this.itemCost > 0) {
        // Convert item cost to cost code letters
        const costNumber = Math.round(this.itemCost * 100); // Convert to cents to avoid decimals
        const letters = this.barcodeSettingsService.convertNumberToCostCodeLetters(costNumber);
        return letters;
      } else if (this.costCode) {
        // Fallback to existing cost code if item cost is not available
        const numericPart = this.costCode.replace(/[^0-9]/g, '');
        if (numericPart) {
          const number = parseInt(numericPart, 10);
          if (!isNaN(number)) {
            const letters = this.barcodeSettingsService.convertNumberToCostCodeLetters(number);
            const prefix = this.costCode.replace(numericPart, '');
            return `${prefix}${letters}`;
          }
        }
        return this.costCode;
      }
    }
    return this.barcode;
  }

  /**
   * Get the barcode value for ngx-barcode element (should always be the original barcode for scanning)
   */
  getBarcodeValue(): string {
    return this.barcode;
  }

  /**
   * Generate preview items array based on rows and columns settings
   */
  getPreviewItems(): any[] {
    const items = [];
    const totalItems = this.numberOfRows * this.numberOfColumns;

    for (let i = 0; i < totalItems; i++) {
      items.push({
        barcode: this.barcode,
        displayCode: this.getDisplayCode(),
        itemName: this.itemName,
        price: this.price
      });
    }
    return items;
  }

}
