import {Component, OnInit} from '@angular/core';
import {CompanyService} from "../../../../core/service/company.service";
import {Company} from "../../../../core/model/company";
import {BsModalRef} from "ngx-bootstrap/modal";
import {BarcodeSettingsService, BarcodeSettings, CostCodeSettings} from "../../../../core/service/barcode-settings.service";

@Component({
  selector: 'app-barcode',
  templateUrl: './barcode.component.html',
  styleUrls: ['./barcode.component.css']
})
export class BarcodeComponent implements OnInit {

  barcode = '';
  itemName = '';
  itemCode = '';
  price = '';
  itemCost = 0; // Item cost for cost code conversion
  noOfCopies = 3;
  numberOfRows = 3;
  numberOfColumns = 2; // Add columns control
  elementType = 'svg';
  format = 'CODE128';
  lineColor = '#000000';
  width = 0.9;
  height = 25;
  displayValue = false;
  fontOptions = '';
  font = 'monospace';
  textAlign = 'center';
  textPosition = 'bottom';
  textMargin = 0;
  fontSize = 9;
  background = '#ffffff';
  margin = 0;
  marginTop = 0;
  marginBottom = 0;
  marginLeft = 0;
  marginRight = 0;

  // Settings
  barcodeSettings: BarcodeSettings;
  costCodeSettings: CostCodeSettings;
  useCostCodes = false;
  showBarcode = true;
  paperSize = '30x20';

  // Make Math available in template
  Math = Math;

  company: Company;
  modalRef: BsModalRef;

  constructor(
    private companyService: CompanyService,
    private barcodeSettingsService: BarcodeSettingsService
  ) {
  }

  ngOnInit() {
    this.findCompany();
    this.company = new Company();
    this.loadSettings();
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
    });
  }

  /**
   * Load barcode and cost code settings
   */
  loadSettings() {
    // Load barcode settings from general settings
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettingsFromGeneralSettings();
    this.previewFontSize = this.barcodeSettings.fontSize;
    this.useCostCodes = this.barcodeSettings.useCostCodes;
    this.showBarcode = this.barcodeSettings.showBarcode;
    this.numberOfColumns = this.barcodeSettings.columns;
    this.paperSize = this.barcodeSettings.paperSize;

    // Load cost code settings
    this.barcodeSettingsService.getCostCodeSettings().subscribe(settings => {
      this.costCodeSettings = settings;
    });
  }

  /**
   * Get the display code for preview (with cost code conversion if enabled)
   */
  getDisplayCode(): string {
    if (this.useCostCodes && this.itemCost > 0) {
      // Convert item cost to cost code letters
      const costNumber = Math.round(this.itemCost * 100); // Convert to cents to avoid decimals
      const letters = this.barcodeSettingsService.convertNumberToCostCodeLetters(costNumber);
      return letters;
    }
    return this.barcode;
  }

  /**
   * Get the barcode value for ngx-barcode element (should always be the original barcode for scanning)
   */
  getBarcodeValue(): string {
    return this.barcode;
  }

  /**
   * Generate preview items array based on rows and columns settings
   */
  getPreviewItems(): any[] {
    const items = [];
    const totalItems = this.numberOfRows * this.numberOfColumns;

    for (let i = 0; i < totalItems; i++) {
      items.push({
        barcode: this.barcode,
        displayCode: this.getDisplayCode(),
        itemName: this.itemName,
        price: this.price
      });
    }
    return items;
  }

  /**
   * Get sticker dimensions based on paper size
   */
  getStickerDimensions(): { width: string, height: string } {
    const dimensions = {
      '30x20': { width: '30mm', height: '20mm' },
      '33x21': { width: '33mm', height: '21mm' },
      '38x25': { width: '38mm', height: '25mm' },
      '50x25': { width: '50mm', height: '25mm' },
      '65x15': { width: '65mm', height: '15mm' },
      '100x50': { width: '100mm', height: '50mm' },
      '100x150': { width: '100mm', height: '150mm' },
      'A4': { width: '90mm', height: '60mm' }, // Default size for A4
      'Letter': { width: '90mm', height: '60mm' } // Default size for Letter
    };
    return dimensions[this.paperSize] || dimensions['A4'];
  }

  /**
   * Get container styles for the preview grid
   */
  getContainerStyles(): string {
    const dimensions = this.getStickerDimensions();
    return `display: grid; grid-template-columns: repeat(${this.numberOfColumns}, ${dimensions.width}); gap: 5mm; padding: 10mm; justify-content: center;`;
  }

  /**
   * Get individual sticker styles
   */
  getStickerStyles(): string {
    const dimensions = this.getStickerDimensions();
    return `border: 1px solid #000; padding: 2mm; text-align: center; background-color: #fff; width: ${dimensions.width}; height: ${dimensions.height}; display: flex; flex-direction: column; justify-content: center; align-items: center; box-sizing: border-box;`;
  }

  /**
   * Get responsive font sizes based on sticker size
   */
  getResponsiveFontSizes(): { code: string, name: string, price: string, barcode: string } {
    // Adjust font sizes based on paper size for better fit
    const sizeMultipliers = {
      '30x20': 0.7,  // Small stickers need smaller fonts
      '33x21': 0.75,
      '38x25': 0.8,
      '50x25': 0.9,
      '65x15': 0.6,  // Very narrow, need small fonts
      '100x50': 1.2,
      '100x150': 1.5, // Large stickers can have bigger fonts
      'A4': 1.0,
      'Letter': 1.0
    };

    const multiplier = sizeMultipliers[this.paperSize] || 1.0;
    const baseFontSize = this.previewFontSize * multiplier;

    return {
      code: `${Math.max(6, baseFontSize)}pt`,
      name: `${Math.max(5, baseFontSize - 2)}pt`,
      price: `${Math.max(5, baseFontSize - 1)}pt`,
      barcode: `${Math.max(5, baseFontSize - 1)}pt`
    };
  }

  print() {
    // Create items array based on the number of copies
    const items = [];

    for (let i = 0; i < this.noOfCopies; i++) {
      items.push({
        barcode: this.barcode,
        itemCode: this.itemCode,
        itemName: this.itemName,
        sellingPrice: parseFloat(this.price) || 0,
        itemCost: this.itemCost
      });
    }

    // Generate HTML using the barcode settings service with specified number of rows
    const printHTML = this.barcodeSettingsService.generateBarcodeHTML(items, this.numberOfRows);

    // Open in new window and print
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.focus();

      // Add a small delay before printing to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    } else {
      alert('Please allow popups for this site to enable printing.');
    }
  }

}
