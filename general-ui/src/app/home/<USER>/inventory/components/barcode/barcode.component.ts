import {Component, OnInit} from '@angular/core';
import {CompanyService} from "../../../../core/service/company.service";
import {Company} from "../../../../core/model/company";
import {BsModalRef} from "ngx-bootstrap/modal";
import {BarcodeSettingsService, BarcodeSettings, CostCodeSettings} from "../../../../core/service/barcode-settings.service";

@Component({
  selector: 'app-barcode',
  templateUrl: './barcode.component.html',
  styleUrls: ['./barcode.component.css']
})
export class BarcodeComponent implements OnInit {

  barcode = '';
  itemName = '';
  itemCode = '';
  price = '';
  costCode = '';
  noOfCopies: 3;
  numberOfRows: 3;
  elementType = 'svg';
  format = 'CODE128';
  lineColor = '#000000';
  width = 0.9;
  height = 25;
  displayValue = false;
  fontOptions = '';
  font = 'monospace';
  textAlign = 'center';
  textPosition = 'bottom';
  textMargin = 0;
  fontSize = 9;
  background = '#ffffff';
  margin = 0;
  marginTop = 0;
  marginBottom = 0;
  marginLeft = 0;
  marginRight = 0;

  // Settings
  barcodeSettings: BarcodeSettings;
  costCodeSettings: CostCodeSettings;
  previewFontSize = 12;
  useCostCodes = false;

  // Make Math available in template
  Math = Math;

  company: Company;
  modalRef: BsModalRef;

  constructor(
    private companyService: CompanyService,
    private barcodeSettingsService: BarcodeSettingsService
  ) {
  }

  ngOnInit() {
    this.findCompany();
    this.company = new Company();
    this.loadSettings();
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
    });
  }

  /**
   * Load barcode and cost code settings
   */
  loadSettings() {
    // Load barcode settings from general settings
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettingsFromGeneralSettings();
    this.previewFontSize = this.barcodeSettings.fontSize;
    this.useCostCodes = this.barcodeSettings.useCostCodes;

    // Load cost code settings
    this.barcodeSettingsService.getCostCodeSettings().subscribe(settings => {
      this.costCodeSettings = settings;
    });
  }

  /**
   * Get the display code for preview (with cost code conversion if enabled)
   */
  getDisplayCode(): string {
    if (this.useCostCodes && this.costCode) {
      // Extract numeric part and convert to letters
      const numericPart = this.costCode.replace(/[^0-9]/g, '');
      if (numericPart) {
        const number = parseInt(numericPart, 10);
        if (!isNaN(number)) {
          const letters = this.barcodeSettingsService.convertNumberToCostCodeLetters(number);
          const prefix = this.costCode.replace(numericPart, '');
          return `${prefix}${letters}`;
        }
      }
      return this.costCode;
    }
    return this.barcode;
  }

  print() {
    // Create items array based on the number of copies
    const items = [];

    for (let i = 0; i < this.noOfCopies; i++) {
      items.push({
        barcode: this.barcode,
        itemCode: this.itemCode,
        itemName: this.itemName,
        sellingPrice: parseFloat(this.price) || 0,
        costCode: this.costCode || this.barcode // Use costCode if available, otherwise barcode
      });
    }

    // Generate HTML using the barcode settings service with specified number of rows
    const printHTML = this.barcodeSettingsService.generateBarcodeHTML(items, this.numberOfRows);

    // Open in new window and print
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.focus();

      // Add a small delay before printing to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    } else {
      alert('Please allow popups for this site to enable printing.');
    }
  }

}
