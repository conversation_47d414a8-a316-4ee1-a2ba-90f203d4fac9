import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from "@angular/router";
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {

  constructor(private router: Router) {
  }

  ngOnInit() {
  }

  /**
   * Get the current route URL
   */
  getCurrentUrl(): string {
    return this.router.url;
  }



  /**
   * Update header type based on current route
   * Use header2 for test_header2 route
   */
  updateHeaderType() {
    const currentUrl = this.getCurrentUrl();
    this.useHeader2 = currentUrl.includes('/home/<USER>');
  }
}
