import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { GeneralSettingsService } from '../../service/general-settings.service';
import { Settings } from '../../../core/model/settings';

interface CostCodeSettings {
  letterMapping: { [key: string]: string };
}

@Component({
  selector: 'app-cost-code-setup',
  templateUrl: './cost-code-setup.component.html',
  styleUrls: ['./cost-code-setup.component.css']
})
export class CostCodeSetupComponent implements OnInit {

  loading: boolean = false;

  // Cost Code Settings - only letter mapping
  costCodeSettings: CostCodeSettings = {
    letterMapping: {
      '0': 'A',
      '1': 'B',
      '2': 'C',
      '3': 'D',
      '4': 'E',
      '5': 'F',
      '6': 'G',
      '7': 'H',
      '8': 'I',
      '9': 'J'
    }
  };

  constructor(
    private toastr: ToastrService,
    private generalSettingsService: GeneralSettingsService
  ) { }

  ngOnInit(): void {
    this.loadCostCodeSettings();
  }

  /**
   * Load cost code settings from localStorage first, then from database if not found
   */
  loadCostCodeSettings(): void {
    this.loading = true;

    // First try to load from localStorage
    try {
      const saved = localStorage.getItem('costCodeLetterMapping');
      if (saved) {
        this.costCodeSettings.letterMapping = JSON.parse(saved);
        console.log('Loaded cost code letter mapping from localStorage');
        this.loading = false;
        return;
      }
    } catch (error) {
      console.error('Error loading cost code settings from localStorage:', error);
    }

    // If not found in localStorage, fetch from database
    this.generalSettingsService.getSettingByKey('costCodeLetterMapping').subscribe({
      next: (setting) => {
        if (setting && setting.value) {
          try {
            this.costCodeSettings.letterMapping = JSON.parse(setting.value);
            // Save to localStorage for future use
            localStorage.setItem('costCodeLetterMapping', setting.value);
            console.log('Loaded cost code letter mapping from database and cached to localStorage');
          } catch (error) {
            console.error('Error parsing cost code letter mapping from database:', error);
            this.toastr.warning('Using default letter mapping due to parsing error', 'Warning');
          }
        } else {
          console.log('No existing cost code letter mapping found in database, using defaults');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading cost code settings from database:', error);
        this.toastr.warning('Could not load settings from database, using defaults', 'Warning');
        this.loading = false;
      }
    });
  }

  /**
   * Save cost code settings to database and localStorage
   */
  saveCostCodeSettings(): void {
    this.loading = true;

    const setting = new Settings();
    setting.id = ''; // Will be set by the backend
    setting.key = 'costCodeLetterMapping';
    setting.value = JSON.stringify(this.costCodeSettings.letterMapping);
    setting.description = 'Number to letter mapping for cost codes';
    setting.category = 'Barcode';

    // Save to database first
    this.generalSettingsService.saveSetting(setting).subscribe({
      next: (response) => {
        // Check if response is successful (could be direct setting object or response object)
        if (response && (response.key || response.success)) {
          // Save to localStorage for faster access
          try {
            localStorage.setItem('costCodeLetterMapping', JSON.stringify(this.costCodeSettings.letterMapping));
            console.log('Saved cost code letter mapping to database and localStorage');
          } catch (localError) {
            console.error('Error saving to localStorage:', localError);
          }
          this.toastr.success('Cost code letter mapping saved successfully', 'Success');
        } else {
          this.toastr.error('Failed to save cost code settings', 'Error');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error saving cost code settings to database:', error);
        // Still save to localStorage as fallback
        try {
          localStorage.setItem('costCodeLetterMapping', JSON.stringify(this.costCodeSettings.letterMapping));
          this.toastr.warning('Saved to local storage only (database error)', 'Warning');
        } catch (localError) {
          this.toastr.error('Failed to save cost code settings', 'Error');
        }
        this.loading = false;
      }
    });
  }

  /**
   * Reset settings to defaults
   */
  resetToDefaults(): void {
    if (confirm('Are you sure you want to reset letter mapping to defaults?')) {
      this.costCodeSettings = {
        letterMapping: {
          '0': 'A',
          '1': 'B',
          '2': 'C',
          '3': 'D',
          '4': 'E',
          '5': 'F',
          '6': 'G',
          '7': 'H',
          '8': 'I',
          '9': 'J'
        }
      };
      this.toastr.success('Letter mapping reset to defaults', 'Success');
    }
  }

  /**
   * Get array of numbers for letter mapping
   */
  getNumbersForMapping(): string[] {
    return ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  }

  /**
   * Convert number to cost code letters based on mapping
   */
  convertNumberToCostCodeLetters(number: number): string {
    const numberStr = number.toString();
    let result = '';

    for (let digit of numberStr) {
      result += this.costCodeSettings.letterMapping[digit] || digit;
    }

    return result;
  }

  /**
   * Get preview example
   */
  getPreviewExample(): string {
    const exampleNumber = 21;
    const letters = this.convertNumberToCostCodeLetters(exampleNumber);
    return `Example: ${exampleNumber} = ${letters}`;
  }
}
