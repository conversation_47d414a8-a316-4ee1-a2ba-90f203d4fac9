/* Header styles - inherited from original header */
.navbar-brand {
  font-weight: bold;
}

.navbar-right i {
  transition: all 0.2s ease;
}

.navbar-right i:hover {
  transform: scale(1.1);
}

.badge {
  font-size: 0.7rem;
}

/* Permissions Menu Styles */
.permissions-menu {
  min-width: 350px;
  max-width: 400px;
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.permissions-grid {
  padding: 0.5rem;
}

.module-item {
  border: 1px solid #e9ecef;
  border-radius: 0.25rem;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.module-item:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.module-header {
  background-color: transparent;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  user-select: none;
}

.module-header:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.module-header.active {
  background-color: rgba(0, 123, 255, 0.15);
  border-color: #007bff;
}

.module-icon {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.module-name {
  font-size: 0.9rem;
  color: #495057;
}

.transition-icon {
  transition: transform 0.2s ease;
  font-size: 0.8rem;
  color: #6c757d;
}

.transition-icon.rotated {
  transform: rotate(180deg);
}

.module-permissions {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.module-permissions.show {
  max-height: 300px;
}

.permission-item {
  background-color: #ffffff;
  border-radius: 0.25rem;
  margin: 0.25rem 0;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.permission-item:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  transform: translateX(2px);
}

.permission-icon {
  font-size: 0.9rem;
  width: 16px;
  text-align: center;
}

.permission-name {
  font-size: 0.85rem;
  color: #495057;
}

/* Dropdown animation */
.dropdown-menu {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none;
}

.dropdown-menu.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Scrollbar styling for permissions menu */
.permissions-menu::-webkit-scrollbar {
  width: 6px;
}

.permissions-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.permissions-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.permissions-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .navbar-right i.fa-2x {
    font-size: 1.25rem !important;
  }

  .navbar-right .mr-3 {
    margin-right: 0.5rem !important;
  }

  .navbar-brand {
    font-size: 1rem;
  }

  .badge {
    font-size: 0.6rem;
    padding: 0.15rem 0.3rem;
  }

  .permissions-menu {
    min-width: 280px;
    max-width: 320px;
    max-height: 400px;
  }

  .module-name {
    font-size: 0.8rem;
  }

  .permission-name {
    font-size: 0.75rem;
  }

  .module-icon {
    font-size: 1rem;
  }

  .permission-icon {
    font-size: 0.8rem;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .module-item {
    background-color: #343a40;
    border-color: #495057;
  }

  .module-item:hover {
    background-color: #495057;
  }

  .permission-item {
    background-color: #495057;
  }

  .permission-item:hover {
    background-color: #6c757d;
  }

  .module-name,
  .permission-name {
    color: #f8f9fa;
  }
}

/* Animation for permission items */
.permission-item {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
