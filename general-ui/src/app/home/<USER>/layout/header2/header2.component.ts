import {Component, Inject, OnInit, HostListener} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {CoreApiConstants} from "../../core-constants";
import {UserService} from "../../../admin/service/user.service";
import {PermissionService} from "../../service/permission.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-header2',
  templateUrl: './header2.component.html',
  styleUrls: ['./header2.component.css']
})
export class Header2Component implements OnInit {

  elem;
  appVersion: string;
  isAdmin: boolean = false;
  user: any;
  
  // Permissions menu properties
  showPermissionsMenu: boolean = false;
  loadingPermissions: boolean = false;
  modulePermissions: Array<any> = [];

  constructor(
    @Inject(DOCUMENT) private document: any, 
    private userService: UserService,
    private permissionService: PermissionService,
    private router: Router
  ) {
    this.appVersion = CoreApiConstants.APP_VERSION;
    this.user = JSON.parse(localStorage.getItem('currentUser'))?.user;
  }

  ngOnInit() {
    this.elem = document.documentElement;
    this.checkIfUserIsAdmin();
    this.loadUserPermissions();
  }

  /**
   * Check if the current user has admin role
   */
  checkIfUserIsAdmin() {
    this.userService.isAdmin().subscribe(
      (isAdmin: boolean) => {
        this.isAdmin = isAdmin;
      },
      (error) => {
        console.error('Error checking admin status:', error);
        this.isAdmin = false;
      }
    );
  }

  /**
   * Load user permissions grouped by module
   */
  loadUserPermissions() {
    if (!this.user?.username) {
      console.error('No user found in localStorage');
      return;
    }

    this.loadingPermissions = true;
    this.permissionService.findAvailablePermissions(this.user.username).subscribe(
      (permissions: Array<any>) => {
        this.processPermissions(permissions);
        this.loadingPermissions = false;
      },
      (error) => {
        console.error('Error loading permissions:', error);
        this.loadingPermissions = false;
      }
    );
  }

  /**
   * Process permissions and group by module
   */
  processPermissions(permissions: Array<any>) {
    this.modulePermissions = [];
    
    permissions.forEach(permission => {
      let existingModule = this.modulePermissions.find(module => 
        module.name === permission.module.name
      );
      
      if (existingModule) {
        existingModule.perms.push({
          name: permission.name,
          route: permission.route,
          iconCss: permission.iconCss
        });
      } else {
        this.modulePermissions.push({
          name: permission.module.name,
          perms: [{
            name: permission.name,
            route: permission.route,
            iconCss: permission.iconCss
          }],
          expanded: false
        });
      }
    });
  }

  /**
   * Toggle permissions menu visibility
   */
  togglePermissionsMenu() {
    this.showPermissionsMenu = !this.showPermissionsMenu;
    
    // Load permissions if not already loaded
    if (this.showPermissionsMenu && this.modulePermissions.length === 0) {
      this.loadUserPermissions();
    }
  }

  /**
   * Toggle module permissions visibility
   */
  toggleModulePermissions(module: any) {
    module.expanded = !module.expanded;
  }

  /**
   * Navigate to permission route
   */
  navigateToPermission(route: string) {
    if (route) {
      this.router.navigate([route]);
      this.showPermissionsMenu = false; // Close menu after navigation
    }
  }

  /**
   * Get icon for module based on module name
   */
  getModuleIcon(moduleName: string): string {
    const moduleIcons = {
      'Inventory': 'fas fa-boxes text-primary',
      'Trade': 'fas fa-handshake text-success',
      'Report': 'fas fa-chart-bar text-info',
      'Admin': 'fas fa-user-cog text-warning',
      'HR': 'fas fa-users text-secondary',
      'Dashboard': 'fas fa-tachometer-alt text-danger',
      'Core': 'fas fa-cog text-dark'
    };
    
    return moduleIcons[moduleName] || 'fas fa-folder text-muted';
  }

  /**
   * Get icon for permission based on permission name
   */
  getPermissionIcon(permissionName: string): string {
    // Common permission icons
    if (permissionName.toLowerCase().includes('view') || permissionName.toLowerCase().includes('list')) {
      return 'fas fa-eye text-info';
    }
    if (permissionName.toLowerCase().includes('create') || permissionName.toLowerCase().includes('add')) {
      return 'fas fa-plus text-success';
    }
    if (permissionName.toLowerCase().includes('edit') || permissionName.toLowerCase().includes('update')) {
      return 'fas fa-edit text-warning';
    }
    if (permissionName.toLowerCase().includes('delete') || permissionName.toLowerCase().includes('remove')) {
      return 'fas fa-trash text-danger';
    }
    if (permissionName.toLowerCase().includes('report')) {
      return 'fas fa-file-alt text-primary';
    }
    if (permissionName.toLowerCase().includes('manage')) {
      return 'fas fa-cogs text-secondary';
    }
    
    return 'fas fa-circle text-muted';
  }

  /**
   * Navigate to dashboard
   */
  navigateToDashboard() {
    console.log('Navigating to dashboard...');
    this.router.navigate(['/home/<USER>']).then(
      (success) => {
        console.log('Navigation success:', success);
      },
      (error) => {
        console.error('Navigation error:', error);
      }
    );
  }

  /**
   * Open fullscreen mode
   */
  openFullscreen() {
    if (this.elem.requestFullscreen) {
      this.elem.requestFullscreen();
    } else if (this.elem.mozRequestFullScreen) {
      this.elem.mozRequestFullScreen();
    } else if (this.elem.webkitRequestFullscreen) {
      this.elem.webkitRequestFullscreen();
    } else if (this.elem.msRequestFullscreen) {
      this.elem.msRequestFullscreen();
    }
  }

  /**
   * Close permissions menu when clicking outside
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.dropdown');
    
    if (!dropdown || !dropdown.querySelector('#permissionsDropdown')) {
      this.showPermissionsMenu = false;
    }
  }

  protected readonly CoreApiConstants = CoreApiConstants;
}
