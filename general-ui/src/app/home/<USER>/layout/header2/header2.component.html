<nav class="navbar navbar-expand-lg theme-color-bg sticky-top ml-auto">
  <div class="navbar-header">
    <a class="navbar-brand text-white" routerLink="../home/<USER>">{{ 'SITE_NAME' | translate }}</a>
    <span class="font-italic text-white small">(Version {{ appVersion }})</span>
  </div>

  <div class="navbar-right ml-auto d-flex align-items-center">

    <!-- Dashboard Icon (Admin Only) -->
    <div *ngIf="isAdmin" class="mr-3" title="{{ 'HEADER.DASHBOARD' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-tachometer-alt cursor-pointer text-white" (click)="navigateToDashboard()"></i>
    </div>

    <!-- Tasks Icon (Checklist) -->
    <div class="mr-3 position-relative" title="{{ 'HEADER.TASKS' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-clipboard-list cursor-pointer text-white" routerLink="home/tasks"></i>
      <span class="badge badge-danger position-absolute" style="top: 0; right: 0;">5</span>
    </div>

    <!-- Desktop Management Icon (Grid Layout) -->
    <div class="mr-3" title="{{ 'HEADER.MANAGE_DESKTOP' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-th-large cursor-pointer text-white" routerLink="./manage_desktop"></i>
    </div>

    <!-- Permissions Menu Dropdown -->
    <div class="mr-3 dropdown" title="User Permissions" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-user-shield cursor-pointer text-white dropdown-toggle" 
         id="permissionsDropdown" 
         data-toggle="dropdown" 
         aria-haspopup="true" 
         aria-expanded="false"
         (click)="togglePermissionsMenu()"></i>
      
      <!-- Permissions Dropdown Menu -->
      <div class="dropdown-menu dropdown-menu-right permissions-menu" 
           [class.show]="showPermissionsMenu"
           aria-labelledby="permissionsDropdown">
        <div class="dropdown-header">
          <strong>User Permissions by Module</strong>
        </div>
        <div class="dropdown-divider"></div>
        
        <!-- Loading State -->
        <div *ngIf="loadingPermissions" class="text-center p-3">
          <div class="spinner-border spinner-border-sm text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <small class="d-block mt-2">Loading permissions...</small>
        </div>

        <!-- Module Icons with Permissions -->
        <div *ngIf="!loadingPermissions" class="permissions-grid p-2">
          <div *ngFor="let module of modulePermissions" 
               class="module-item mb-2"
               (click)="toggleModulePermissions(module)">
            
            <!-- Module Header -->
            <div class="module-header d-flex align-items-center p-2 rounded cursor-pointer"
                 [class.active]="module.expanded">
              <i [class]="getModuleIcon(module.name)" class="module-icon mr-2"></i>
              <span class="module-name font-weight-bold">{{ module.name }}</span>
              <i class="fas fa-chevron-down ml-auto transition-icon" 
                 [class.rotated]="module.expanded"></i>
            </div>

            <!-- Module Permissions (Collapsible) -->
            <div class="module-permissions" [class.show]="module.expanded">
              <div *ngFor="let permission of module.perms" 
                   class="permission-item d-flex align-items-center p-2 ml-3"
                   (click)="navigateToPermission(permission.route); $event.stopPropagation()">
                <i [class]="getPermissionIcon(permission.name)" class="permission-icon mr-2"></i>
                <span class="permission-name">{{ permission.name }}</span>
              </div>
              
              <!-- No permissions message -->
              <div *ngIf="module.perms.length === 0" class="text-muted text-center p-2 ml-3">
                <small>No permissions available</small>
              </div>
            </div>
          </div>

          <!-- No modules message -->
          <div *ngIf="modulePermissions.length === 0" class="text-center p-3">
            <i class="fas fa-exclamation-circle text-warning"></i>
            <small class="d-block mt-2">No permissions found</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Logout Icon -->
    <div class="mr-3" title="{{ 'HEADER.LOGOUT' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-power-off cursor-pointer text-white" routerLink="../login"></i>
    </div>

    <!-- POS (Sales) Icon -->
    <div class="mr-3" title="{{ 'HEADER.POS' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-cash-register cursor-pointer text-white" routerLink="/invoice_router" (click)="openFullscreen()"></i>
    </div>

    <!-- Menu Icon -->
    <div title="{{ 'HEADER.MENU' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-bars cursor-pointer text-white" routerLink="./all_availablePerms"></i>
    </div>
  </div>
</nav>
