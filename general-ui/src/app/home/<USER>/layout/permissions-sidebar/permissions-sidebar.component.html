<!-- Right Sidebar for Permissions -->
<div class="permissions-sidebar" [class.visible]="sidebarVisible">
  <!-- Sidebar Header -->
  <div class="sidebar-header">
    <h5 class="mb-0">
      <i class="fas fa-user-shield mr-2"></i>
      All Permissions
    </h5>
    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeSidebar()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Sidebar Content -->
  <div class="sidebar-content">
    <!-- Loading State -->
    <div *ngIf="loading" class="text-center p-4">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <p class="mt-3 text-muted">Loading permissions...</p>
    </div>

    <!-- Permissions Content -->
    <div *ngIf="!loading" class="permissions-content">
      <!-- Module Groups -->
      <div *ngFor="let module of modulePermissions" class="module-group mb-3">
        <!-- Module Header -->
        <div class="module-header" (click)="toggleModule(module)">
          <div class="d-flex align-items-center">
            <i [class]="getModuleIcon(module.name)" class="module-icon mr-3"></i>
            <span class="module-title">{{ module.name }}</span>
            <i class="fas fa-chevron-down ml-auto transition-icon" 
               [class.rotated]="module.expanded"></i>
          </div>
        </div>

        <!-- Module Permissions -->
        <div class="module-permissions" [class.expanded]="module.expanded">
          <div *ngFor="let permission of module.perms" 
               class="permission-item"
               (click)="navigateToPermission(permission.route)">
            <i [class]="getPermissionIcon(permission.name)" class="permission-icon mr-2"></i>
            <span class="permission-name">{{ permission.name }}</span>
          </div>

          <!-- No permissions message -->
          <div *ngIf="module.perms.length === 0" class="no-permissions">
            <i class="fas fa-info-circle mr-2"></i>
            <span>No permissions available</span>
          </div>
        </div>
      </div>

      <!-- No modules message -->
      <div *ngIf="modulePermissions.length === 0" class="no-modules text-center p-4">
        <i class="fas fa-exclamation-circle fa-2x text-warning mb-3"></i>
        <h6>No Permissions Found</h6>
        <p class="text-muted">No permissions are available for your account.</p>
      </div>
    </div>
  </div>
</div>

<!-- Backdrop -->
<div class="sidebar-backdrop" 
     [class.visible]="sidebarVisible" 
     (click)="closeSidebar()"></div>
