import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { PermissionsSidebarService } from '../../service/permissions-sidebar.service';

@Component({
  selector: 'app-permissions-sidebar',
  templateUrl: './permissions-sidebar.component.html',
  styleUrls: ['./permissions-sidebar.component.css']
})
export class PermissionsSidebarComponent implements OnInit, OnDestroy {

  sidebarVisible: boolean = false;
  loading: boolean = false;
  modulePermissions: Array<any> = [];

  private subscriptions: Subscription = new Subscription();

  constructor(
    private permissionsSidebarService: PermissionsSidebarService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Subscribe to sidebar visibility
    this.subscriptions.add(
      this.permissionsSidebarService.sidebarVisible$.subscribe(visible => {
        this.sidebarVisible = visible;
      })
    );

    // Subscribe to loading state
    this.subscriptions.add(
      this.permissionsSidebarService.loading$.subscribe(loading => {
        this.loading = loading;
      })
    );

    // Subscribe to permissions data
    this.subscriptions.add(
      this.permissionsSidebarService.permissions$.subscribe(permissions => {
        this.processPermissions(permissions);
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Process permissions and group by module
   */
  processPermissions(permissions: Array<any>) {
    this.modulePermissions = [];
    
    console.log('Sidebar processing permissions:', permissions); // Debug log
    
    permissions.forEach(permission => {
      let existingModule = this.modulePermissions.find(module => 
        module.name === permission.module.name
      );
      
      if (existingModule) {
        existingModule.perms.push({
          name: permission.name,
          route: permission.route,
          iconCss: permission.iconCss
        });
      } else {
        this.modulePermissions.push({
          name: permission.module.name,
          perms: [{
            name: permission.name,
            route: permission.route,
            iconCss: permission.iconCss
          }],
          expanded: false
        });
      }
    });
    
    console.log('Sidebar processed module permissions:', this.modulePermissions); // Debug log
  }

  /**
   * Toggle module expansion
   */
  toggleModule(module: any) {
    module.expanded = !module.expanded;
  }

  /**
   * Navigate to permission route
   */
  navigateToPermission(route: string) {
    if (route) {
      // Add /home/<USER>
      const fullRoute = `/home/<USER>
      console.log('Sidebar navigating to:', fullRoute); // Debug log
      this.router.navigateByUrl(fullRoute);
      this.closeSidebar(); // Close sidebar after navigation
    }
  }

  /**
   * Close the sidebar
   */
  closeSidebar() {
    this.permissionsSidebarService.hideSidebar();
  }

  /**
   * Get icon for module based on module name
   */
  getModuleIcon(moduleName: string): string {
    const moduleIcons = {
      'Inventory': 'fas fa-boxes text-primary',
      'Trade': 'fas fa-handshake text-success',
      'Report': 'fas fa-chart-bar text-info',
      'Admin': 'fas fa-user-cog text-warning',
      'HR': 'fas fa-users text-secondary',
      'Dashboard': 'fas fa-tachometer-alt text-danger',
      'Core': 'fas fa-cog text-dark'
    };
    
    return moduleIcons[moduleName] || 'fas fa-folder text-muted';
  }

  /**
   * Get icon for permission based on permission name
   */
  getPermissionIcon(permissionName: string): string {
    // Common permission icons
    if (permissionName.toLowerCase().includes('view') || permissionName.toLowerCase().includes('list')) {
      return 'fas fa-eye text-info';
    }
    if (permissionName.toLowerCase().includes('create') || permissionName.toLowerCase().includes('add')) {
      return 'fas fa-plus text-success';
    }
    if (permissionName.toLowerCase().includes('edit') || permissionName.toLowerCase().includes('update')) {
      return 'fas fa-edit text-warning';
    }
    if (permissionName.toLowerCase().includes('delete') || permissionName.toLowerCase().includes('remove')) {
      return 'fas fa-trash text-danger';
    }
    if (permissionName.toLowerCase().includes('report')) {
      return 'fas fa-file-alt text-primary';
    }
    if (permissionName.toLowerCase().includes('manage')) {
      return 'fas fa-cogs text-secondary';
    }
    
    return 'fas fa-circle text-muted';
  }
}
