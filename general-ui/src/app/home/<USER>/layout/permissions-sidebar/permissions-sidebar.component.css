/* Right Sidebar Styles */
.permissions-sidebar {
  position: fixed;
  top: 0;
  right: -400px; /* Hidden by default */
  width: 400px;
  height: 100vh;
  background-color: #ffffff;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1050;
  transition: right 0.3s ease;
  border-left: 1px solid #dee2e6;
}

.permissions-sidebar.visible {
  right: 0; /* Slide in from right */
}

/* Sidebar Header */
.sidebar-header {
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sidebar-header h5 {
  color: #495057;
  font-weight: 600;
}

/* Sidebar Content */
.sidebar-content {
  height: calc(100vh - 80px); /* Subtract header height */
  overflow-y: auto;
  padding: 1rem;
}

.permissions-content {
  padding-bottom: 2rem;
}

/* Module Groups */
.module-group {
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  background-color: #f8f9fa;
  overflow: hidden;
}

.module-header {
  padding: 1rem 1.25rem;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.module-header:hover {
  background-color: #f8f9fa;
}

.module-icon {
  font-size: 1.25rem;
  width: 24px;
  text-align: center;
}

.module-title {
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
}

.transition-icon {
  transition: transform 0.2s ease;
  color: #6c757d;
  font-size: 0.875rem;
}

.transition-icon.rotated {
  transform: rotate(180deg);
}

/* Module Permissions */
.module-permissions {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: #ffffff;
}

.module-permissions.expanded {
  max-height: 500px;
}

.permission-item {
  padding: 0.75rem 1.25rem 0.75rem 2.5rem;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.permission-item:hover {
  background-color: #f8f9fa;
  padding-left: 2.75rem;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-icon {
  font-size: 0.875rem;
  width: 16px;
  text-align: center;
}

.permission-name {
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
}

/* No permissions/modules messages */
.no-permissions,
.no-modules {
  color: #6c757d;
  font-size: 0.875rem;
  padding: 1rem 1.25rem;
  text-align: center;
}

.no-permissions {
  padding-left: 2.5rem;
  text-align: left;
}

/* Backdrop */
.sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-backdrop.visible {
  opacity: 1;
  visibility: visible;
}

/* Loading State */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Scrollbar Styling */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .permissions-sidebar {
    width: 320px;
    right: -320px;
  }

  .sidebar-header {
    padding: 0.75rem 1rem;
  }

  .sidebar-header h5 {
    font-size: 1rem;
  }

  .sidebar-content {
    padding: 0.75rem;
  }

  .module-header {
    padding: 0.75rem 1rem;
  }

  .module-title {
    font-size: 0.9rem;
  }

  .permission-item {
    padding: 0.625rem 1rem 0.625rem 2rem;
  }

  .permission-item:hover {
    padding-left: 2.25rem;
  }

  .permission-name {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .permissions-sidebar {
    width: 280px;
    right: -280px;
  }
}

/* Animation for permission items */
.permission-item {
  animation: slideInRight 0.2s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
