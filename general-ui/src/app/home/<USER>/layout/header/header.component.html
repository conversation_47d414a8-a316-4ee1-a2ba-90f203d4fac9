<nav class="navbar navbar-expand-lg theme-color-bg sticky-top ml-auto">
  <div class="navbar-header">
    <a class="navbar-brand text-white" routerLink="../home/<USER>">{{ 'SITE_NAME' | translate }}</a>
    <span class="font-italic text-white small">(Version {{ appVersion }})</span>
  </div>

  <div class="navbar-right ml-auto d-flex align-items-center">

    <!-- Dashboard Icon (Admin Only) -->
    <div *ngIf="isAdmin" class="mr-3" title="{{ 'HEADER.DASHBOARD' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-tachometer-alt cursor-pointer text-white" (click)="navigateToDashboard()"></i>
    </div>

    <!-- Tasks Icon (Checklist) -->
    <div class="mr-3 position-relative" title="{{ 'HEADER.TASKS' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-clipboard-list cursor-pointer text-white" routerLink="home/tasks"></i>
      <span class="badge badge-danger position-absolute" style="top: 0; right: 0;">5</span>
    </div>

    <!-- Desktop Management Icon (Grid Layout) -->
    <div class="mr-3" title="{{ 'HEADER.MANAGE_DESKTOP' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-th-large cursor-pointer text-white" routerLink="./manage_desktop"></i>
    </div>

    <!-- User Settings Icon removed - now managed by admin -->


    <!-- Logout Icon (Updated to a door exit icon) -->
    <div class="mr-3" title="{{ 'HEADER.LOGOUT' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-power-off cursor-pointer text-white" routerLink="../login"></i>
    </div>

    <!-- POS (Sales) Icon (Updated to a cash register icon) -->
    <div class="mr-3" title="{{ 'HEADER.POS' | translate }}" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-cash-register cursor-pointer text-white" routerLink="/invoice_router" (click)="openFullscreen()"></i>
    </div>

    <!-- Permissions Menu Button -->
    <div title="All Permissions" data-toggle="tooltip" data-placement="bottom">
      <i class="fas fa-2x fa-chevron-down cursor-pointer text-white"
         (click)="togglePermissionsSidebar()"></i>
    </div>
  </div>
</nav>
