import {Component, Inject, OnInit, HostListener} from '@angular/core';
import {DOCUMENT} from "@angular/common";
import {CoreApiConstants} from "../../core-constants";
import {UserService} from "../../../admin/service/user.service";
import {PermissionService} from "../../service/permission.service";
import {PermissionsSidebarService} from "../../service/permissions-sidebar.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit {

  elem;
  appVersion: string;
  isAdmin: boolean = false;
  user: any;

  constructor(
    @Inject(DOCUMENT) private document: any,
    private userService: UserService,
    private permissionService: PermissionService,
    private permissionsSidebarService: PermissionsSidebarService,
    private router: Router
  ) {
    this.appVersion = CoreApiConstants.APP_VERSION;
    this.user = JSON.parse(localStorage.getItem('currentUser'))?.user;
  }

  ngOnInit() {
    this.elem = document.documentElement;
    this.checkIfUserIsAdmin();
  }

  /**
   * Check if the current user has admin role
   */
  checkIfUserIsAdmin() {
    this.userService.isAdmin().subscribe(
      (isAdmin: boolean) => {
        this.isAdmin = isAdmin;
      },
      (error) => {
        console.error('Error checking admin status:', error);
        this.isAdmin = false;
      }
    );
  }

  /**
   * Navigate to dashboard
   */
  navigateToDashboard() {
    console.log('Navigating to dashboard...');
    this.router.navigate(['/home/<USER>']).then(
      (success) => {
        console.log('Navigation success:', success);
      },
      (error) => {
        console.error('Navigation error:', error);
      }
    );
  }

  openFullscreen() {
    if (this.elem.requestFullscreen) {
      this.elem.requestFullscreen();
    } else if (this.elem.mozRequestFullScreen) {
      this.elem.mozRequestFullScreen();
    } else if (this.elem.webkitRequestFullscreen) {
      this.elem.webkitRequestFullscreen();
    } else if (this.elem.msRequestFullscreen) {
      this.elem.msRequestFullscreen();
    }
  }

  /**
   * Toggle permissions sidebar
   */
  togglePermissionsSidebar() {
    if (!this.permissionsSidebarService.isSidebarVisible()) {
      // Load permissions when opening sidebar
      this.loadUserPermissions();
    }
    this.permissionsSidebarService.toggleSidebar();
  }

  /**
   * Load user permissions and send to sidebar service
   */
  loadUserPermissions() {
    if (!this.user?.username) {
      console.error('No user found in localStorage');
      return;
    }

    this.permissionsSidebarService.setLoading(true);
    this.permissionService.findAvailablePermissions(this.user.username).subscribe(
      (permissions: Array<any>) => {
        console.log('Loading permissions for sidebar:', permissions); // Debug log
        this.permissionsSidebarService.setPermissions(permissions);
        this.permissionsSidebarService.setLoading(false);
      },
      (error) => {
        console.error('Error loading permissions:', error);
        this.permissionsSidebarService.setLoading(false);
      }
    );
  }



  protected readonly CoreApiConstants = CoreApiConstants;
}
